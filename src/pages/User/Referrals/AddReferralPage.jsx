import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import MkdSDK from "Utils/MkdSDK";
import { Toast } from "Components/Toast";

const EmojiIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M10 0C4.48 0 0 4.48 0 10C0 15.52 4.48 20 10 20C15.52 20 20 15.52 20 10C20 4.48 15.52 0 10 0ZM10 18C5.58 18 2 14.42 2 10C2 5.58 5.58 2 10 2C14.42 2 18 5.58 18 10C18 14.42 14.42 18 10 18ZM7 6C7 6.82843 6.32843 7.5 5.5 7.5C4.67157 7.5 4 6.82843 4 6C4 5.17157 4.67157 4.5 5.5 4.5C6.32843 4.5 7 5.17157 7 6ZM16 6C16 6.82843 15.3284 7.5 14.5 7.5C13.6716 7.5 13 6.82843 13 6C13 5.17157 13.6716 4.5 14.5 4.5C15.3284 4.5 16 5.17157 16 6ZM10 15.5C12.33 15.5 14.32 14.05 15.12 12H4.88C5.68 14.05 7.67 15.5 10 15.5Z" fill="#B5B5B5"/>
  </svg>
);

const AttachmentIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M17.5 9.375L11.25 15.625C10.2083 16.6667 8.89583 17.1875 7.3125 17.1875C5.72917 17.1875 4.41667 16.6667 3.375 15.625C2.33333 14.5833 1.8125 13.2708 1.8125 11.6875C1.8125 10.1042 2.33333 8.79167 3.375 7.75L10.9375 0.1875C11.6458 -0.520833 12.5104 -0.875 13.5312 -0.875C14.5521 -0.875 15.4167 -0.520833 16.125 0.1875C16.8333 0.895833 17.1875 1.76042 17.1875 2.78125C17.1875 3.80208 16.8333 4.66667 16.125 5.375L8.5625 12.9375C8.20833 13.2917 7.78125 13.4688 7.28125 13.4688C6.78125 13.4688 6.35417 13.2917 6 12.9375C5.64583 12.5833 5.46875 12.1562 5.46875 11.6562C5.46875 11.1562 5.64583 10.7292 6 10.375L12.8125 3.5625" stroke="#B5B5B5" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
  </svg>
);

const AddReferralPage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const [joinedCommunities, setJoinedCommunities] = useState([]);
  const [users, setUsers] = useState([]);
  const [fetchingUsers, setFetchingUsers] = useState(false);
  const [selectedType, setSelectedType] = useState(location.state?.referralType || '');
  const [selectedCommunity, setSelectedCommunity] = useState(location.state?.communityId || '');
  const [validationErrors, setValidationErrors] = useState({});
  const [uploadingAttachment, setUploadingAttachment] = useState(false);
  const [attachmentPreview, setAttachmentPreview] = useState("");
  const [attachmentName, setAttachmentName] = useState("");
  const [industries, setIndustries] = useState([]);
  const [formData, setFormData] = useState({
    title: "",
    type: "",
    industry: "",
    description: "",
    know_client: "",
    deal_size: "",
    referral_fee: "",
    payment_method: "",
    referral_type: location.state?.referralType || "",
    community: location.state?.communityId || "",
    direct_person: "",
    additional_notes: "",
    requirements: "",
    description_image: "",
    expiration_date: ""
  });

  useEffect(() => {
    loadJoinedCommunities();
    loadIndustries();
  }, []);

  useEffect(() => {
    if (location.state?.communityId && location.state?.referralType === "community referral") {
      setSelectedType("community referral");
      setSelectedCommunity(location.state.communityId);
      setFormData(prev => ({
        ...prev,
        referral_type: "community referral",
        community: location.state.communityId
      }));
    }
  }, [location.state]);
  const sdk = new MkdSDK();
const yy = sdk.callRawAPI("/user/recommendations",{},"GET")
console.log(yy)

  const loadJoinedCommunities = async () => {
    try {
      const sdk = new MkdSDK();
      const response = await sdk.GetJoinedCommunities();

      if (!response.error) {
        setJoinedCommunities(response.list || []);
      } else {
        setError(response.message);
      }
    } catch (err) {
      setError(err.message || "Failed to load communities");
    }
  };

  const loadIndustries = async () => {
    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI("/v1/api/dealmaker/industries", {}, "GET");

      if (!response.error && response.data) {
        setIndustries(response.data);
      } else {
        setError(response.message || "Failed to load industries");
      }
    } catch (err) {
      setError(err.message || "Failed to load industries");
    }
  };

  // Add default community if none found
  useEffect(() => {
    if (joinedCommunities.length === 0) {
      setJoinedCommunities([{
        id: { value: 1 },
        title: { value: "Rain Maker LLC" }
      }]);
    }
  }, [joinedCommunities]);

  const fetchUsers = async (communityId) => {
    try {
      const sdk = new MkdSDK();
      const response = await sdk.GetCommunityUsers(communityId);
      if (!response.error) {
        // Filter out users with admin role
        const filteredUsers = (response.list || []).filter(user =>
          user.role?.value !== 'admin'
        );
        setUsers(filteredUsers);
      }
    } catch (err) {
      console.error("Failed to fetch users:", err);
      setError(err.message || "Failed to fetch users");
    }
  };

  const handleCommunityChange = (e) => {
    const communityId = e.target.value;
    setSelectedCommunity(communityId);
    formData.community = communityId;
    if (selectedType === 'direct referral') {
      setFetchingUsers(true);
      fetchUsers(communityId);
      setFetchingUsers(false);
    }
  };

  const handleTypeChange = (e) => {
    const type = e.target.value;
    setSelectedType(type);
    // Reset related fields when type changes
    setSelectedCommunity('');
    setUsers([]);
    setFormData(prev => ({
      ...prev,
      referral_type: type,
      community: '',
      direct_person: '',
      requirements: ''
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    // Check for empty required fields
    const requiredFields = {
      title: "Title",
      type: "Type of Opportunity",
      description: "Description",
      deal_size: "Deal Size",
      referral_fee: "Referral Fee",
      payment_method: "Payment Method",
      referral_type: "Referral Type"
    };

    // Additional conditional required fields based on referral type
    if (formData.referral_type === "community referral" || formData.referral_type === "direct referral") {
      requiredFields.community = "Community";
    }
    if (formData.referral_type === "direct referral") {
      requiredFields.direct_person = "Direct Person";
    }

    // Check each required field
    const errors = {};
    let hasErrors = false;

    for (const [field, label] of Object.entries(requiredFields)) {
      if (!formData[field]) {
        errors[field] = `${label} is required`;
        hasErrors = true;
      }
    }

    // Validate expiration date if provided
    if (formData.expiration_date) {
      const selectedDate = new Date(formData.expiration_date);
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Reset time part for accurate date comparison

      if (selectedDate < today) {
        errors.expiration_date = "Expiration date cannot be in the past";
        hasErrors = true;
      }
    }

    setValidationErrors(errors);

    if (hasErrors) {
      setLoading(false);
      return;
    }

    try {
      const sdk = new MkdSDK();

      // Prepare the data for the API - convert industry to industry_id as integer
      const apiData = {
        ...formData,
        industry_id: formData.industry ? parseInt(formData.industry, 10) : null,
        // Remove the string industry field since we're sending industry_id
        industry: undefined
      };

      const response = await sdk.CreateReferral(apiData);

      if (!response.error) {
        navigate('/member/referrals', { state: { activeTab: 'my-referrals' } });
      } else {
        setError(response.message);
      }
    } catch (err) {
      setError(err.message || "Failed to create referral");
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear validation error for this field when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleAttachmentUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    try {
      setUploadingAttachment(true);
      const sdk = new MkdSDK();
      const formData = new FormData();
      formData.append("file", file);

      const result = await sdk.uploadImage(formData);
      if (result.url) {
        setFormData(prev => ({ ...prev, description_image: result.url }));
        setAttachmentPreview(URL.createObjectURL(file));
        setAttachmentName(file.name);
      }
    } catch (err) {
      console.error("Error uploading attachment:", err);
      setError("Failed to upload attachment");
    } finally {
      setUploadingAttachment(false);
    }
  };

  return (
    <div style={{
      width:"600px",
      margin:"20px auto",
    }} className="min-h-screen bg-[#161616] p-6">
      <div className="mx-auto max-w-3xl">
        <h2 className="mb-6 text-xl font-semibold text-[#eaeaea]">Add Opportunity</h2>

        <form onSubmit={handleSubmit} className="space-y-5">
          <div>
            <label className="mb-2 block text-sm text-[#b5b5b5]">Title</label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              placeholder="Enter referral title"
              className="h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea] placeholder-[#666]"
            />
            {validationErrors.title && (
              <p className="mt-1 text-sm text-red-500">{validationErrors.title}</p>
            )}
          </div>

          <div>
            <label className="mb-2 block text-sm text-[#b5b5b5]">Type of Opportunity</label>
            <select
              value={formData.type}
              onChange={(e) => handleInputChange('type', e.target.value)}
              className="h-10 w-full appearance-none rounded-lg border border-[#363636] bg-[#242424] bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNkw4IDEwTDEyIDYiIHN0cm9rZT0iI2I1YjViNSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==')] bg-[length:16px_16px] bg-[right_16px_center] bg-no-repeat px-4 text-[#eaeaea]"
            >
              <option value="">Select type</option>
              <option value="looking_for_service">Looking for Service</option>
              <option value="looking_for_product">Looking for Product</option>
              <option value="looking_for_buyer">Looking for Buyer</option>
              <option value="looking_for_investor">Looking for Investor</option>
              <option value="looking_for_partner">Looking for Partner</option>
            </select>
            {validationErrors.type && (
              <p className="mt-1 text-sm text-red-500">{validationErrors.type}</p>
            )}
          </div>

          <div>
            <label className="mb-2 block text-sm text-[#b5b5b5]">Industry</label>
            <select
              value={formData.industry}
              onChange={(e) => handleInputChange('industry', e.target.value)}
              className="h-10 w-full appearance-none rounded-lg border border-[#363636] bg-[#242424] bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNkw4IDEwTDEyIDYiIHN0cm9rZT0iI2I1YjViNSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==')] bg-[length:16px_16px] bg-[right_16px_center] bg-no-repeat px-4 text-[#eaeaea]"
            >
              <option value="">Select industry</option>
              {industries.map((industry) => (
                <option key={industry.id} value={industry.id}>
                  {industry.name}
                </option>
              ))}
            </select>
            {validationErrors.industry && (
              <p className="mt-1 text-sm text-red-500">{validationErrors.industry}</p>
            )}
          </div>

          <div>
            <label className="mb-2 block text-sm text-[#b5b5b5]">Description</label>
            <div className="relative">
              <textarea
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Write your text here..."
                rows={4}
                className="w-full rounded-lg border border-[#363636] bg-[#242424] p-4 text-[#eaeaea] placeholder-[#666]"
              />
              <div className="absolute bottom-4 right-4 flex items-center gap-2">
                <div className="relative">
                  <input
                    type="file"
                    onChange={handleAttachmentUpload}
                    className="hidden"
                    id="attachment-input"
                    accept="image/*,.pdf,.doc,.docx"
                    disabled={uploadingAttachment}
                  />
                  <button
                    type="button"
                    onClick={() => document.getElementById('attachment-input').click()}
                    className={`text-[#b5b5b5] hover:text-[#eaeaea] ${uploadingAttachment ? 'opacity-50 cursor-not-allowed' : ''}`}
                    disabled={uploadingAttachment}
                  >
                    <AttachmentIcon />
                  </button>
                  {uploadingAttachment && (
                    <div className="absolute -top-1 -right-1 h-2 w-2">
                      <div className="animate-ping absolute h-full w-full rounded-full bg-[#7dd87d] opacity-75"></div>
                      <div className="rounded-full h-full w-full bg-[#7dd87d]"></div>
                    </div>
                  )}
                </div>
                <button
                  type="button"
                  className="text-[#b5b5b5] hover:text-[#eaeaea]"
                  onClick={() => {/* Emoji functionality will be added later */}}
                >
                  {/* <EmojiIcon /> */}
                </button>
              </div>
            </div>
            {attachmentPreview && (
              <div className="mt-2 flex items-center gap-2 rounded-lg border border-[#363636] bg-[#1a1a1a] p-2">
                <div className="flex-1 truncate text-sm text-[#eaeaea]">
                  {attachmentName}
                </div>
                <button
                  type="button"
                  onClick={() => {
                    setFormData(prev => ({ ...prev, description_image: "" }));
                    setAttachmentPreview("");
                    setAttachmentName("");
                  }}
                  className="text-[#b5b5b5] hover:text-[#eaeaea]"
                >
                  <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            )}
            {validationErrors.description && (
              <p className="mt-1 text-sm text-red-500">{validationErrors.description}</p>
            )}
          </div>


          <div className="grid grid-cols-1 gap-4">

            <div>
              <label className="mb-2 block text-sm text-[#b5b5b5]">Estimated Deal Size</label>
              <input
                type="text"
                value={formData.deal_size}
                onChange={(e) => handleInputChange('deal_size', e.target.value)}
                placeholder="e.g., $500"
                className="h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea] placeholder-[#666]"
              />
              {validationErrors.deal_size && (
                <p className="mt-1 text-sm text-red-500">{validationErrors.deal_size}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="mb-2 block text-sm text-[#b5b5b5]">Referral Fee (%)</label>
              <input
                type="text"
                value={formData.referral_fee}
                onChange={(e) => handleInputChange('referral_fee', e.target.value)}
                placeholder="Enter percentage"
                className="h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea] placeholder-[#666]"
              />
              {validationErrors.referral_fee && (
                <p className="mt-1 text-sm text-red-500">{validationErrors.referral_fee}</p>
              )}
            </div>
            <div>
              <label className="mb-2 block text-sm text-[#b5b5b5]">Payment Method</label>
              <select
                value={formData.payment_method}
                onChange={(e) => handleInputChange('payment_method', e.target.value)}
                className="h-10 w-full appearance-none rounded-lg border border-[#363636] bg-[#242424] bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNkw4IDEwTDEyIDYiIHN0cm9rZT0iI2I1YjViNSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==')] bg-[length:16px_16px] bg-[right_16px_center] bg-no-repeat px-4 text-[#eaeaea]"
              >
                <option value="">Select method</option>
                <option value="bank">Bank Transfer</option>
                <option value="bank">Bank Card</option>
              </select>
              {validationErrors.payment_method && (
                <p className="mt-1 text-sm text-red-500">{validationErrors.payment_method}</p>
              )}
            </div>
          </div>

          <div>
            <label className="mb-2 block text-sm text-[#b5b5b5]">Referral Type</label>
            <select
              value={formData.referral_type}
              onChange={handleTypeChange}
              disabled={location.state?.referralType === "community referral"}
              className="h-10 w-full appearance-none rounded-lg border border-[#363636] bg-[#242424] bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNkw4IDEwTDEyIDYiIHN0cm9rZT0iI2I1YjViNSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==')] bg-[length:16px_16px] bg-[right_16px_center] bg-no-repeat px-4 text-[#eaeaea]"
            >
              <option value="">Select type</option>
              <option value="open referral">Open Referral</option>
              <option value="community referral">Community Referral</option>
              <option value="direct referral">Direct Referral</option>
            </select>
            {validationErrors.referral_type && (
              <p className="mt-1 text-sm text-red-500">{validationErrors.referral_type}</p>
            )}
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
                {(selectedType === "community referral" || selectedType === "direct referral") && (<div>
                <label className="mb-2 block text-sm text-[#b5b5b5]">Select Community</label>
                <select
                  value={formData.community}
                  onChange={(e) => handleCommunityChange(e)}
                  disabled={location.state?.communityId}
                  className="h-10 w-full appearance-none rounded-lg border border-[#363636] bg-[#242424] bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNkw4IDEwTDEyIDYiIHN0cm9rZT0iI2I1YjViNSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==')] bg-[length:16px_16px] bg-[right_16px_center] bg-no-repeat px-4 text-[#eaeaea] placeholder-[#666]"
                  required
                >
                  <option value="" disabled>Select community</option>
                  {joinedCommunities.map((community) => (
                    <option
                      key={community.id.value}
                      value={community.id.value}
                    >
                      {community.title.value}
                    </option>
                  ))}
                </select>
              </div>)}
            </div>

            <div>
              {selectedType === "direct referral" && (
                <div>
                  <label className="mb-2 block text-sm text-[#b5b5b5]">Select Direct Person</label>
                  {fetchingUsers ? (
                    <div className="h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea] placeholder-[#666]">
                      <div className="flex items-center justify-center">
                        <div style={{
                      marginTop: "8px",
                    }} className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-[#eaeaea]"></div>
                      </div>
                    </div>
                  ) : (
                    <select
                      value={formData.direct_person}
                      onChange={(e) => setFormData(prev => ({ ...prev, direct_person: e.target.value }))}
                    className="h-10 w-full appearance-none rounded-lg border border-[#363636] bg-[#242424] bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNkw4IDEwTDEyIDYiIHN0cm9rZT0iI2I1YjViNSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==')] bg-[length:16px_16px] bg-[right_16px_center] bg-no-repeat px-4 text-[#eaeaea] placeholder-[#666]"
                  >
                    <option value="">Select person</option>
                    {users.map((user) => (
                      <option key={user.id.value} value={user.id.value}>
                        {user.name.value}
                      </option>
                    ))}
                  </select>
                )}
              </div>
              )}
            </div>
          </div>

          <div>
            <label className="mb-2 block text-sm text-[#b5b5b5]">Expiration Date</label>
            <input
              type="date"
              value={formData.expiration_date}
              onChange={(e) => handleInputChange('expiration_date', e.target.value)}
              className="h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]"
              min={new Date().toISOString().split('T')[0]} // Set minimum date to today
            />
            <p className="mt-1 text-xs text-[#666]">The opportunity will expire on this date. Leave blank for no expiration.</p>
            {validationErrors.expiration_date && (
              <p className="mt-1 text-sm text-red-500">{validationErrors.expiration_date}</p>
            )}
          </div>

          <div>
            <label className="mb-2 block text-sm text-[#b5b5b5]">Additional Notes</label>
            <textarea
              value={formData.additional_notes}
              onChange={(e) => setFormData(prev => ({ ...prev, additional_notes: e.target.value }))}
              placeholder="Write any additional notes here..."
              rows={4}
              className="w-full rounded-lg border border-[#363636] bg-[#242424] p-4 text-[#eaeaea] placeholder-[#666]"
            />
          </div>

          <div className="flex justify-end gap-4">
            <button
              type="button"
              onClick={() => navigate('/member/referrals', { state: { activeTab: 'my-referrals' } })}
              className="rounded-lg border border-[#363636] px-4 py-2 text-sm text-[#eaeaea] hover:bg-[#363636]"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea] hover:bg-[#2e7d32]/90"
            >
              Submit Referral
            </button>
          </div>
        </form>

        {error && <Toast message={error} />}
      </div>
    </div>
  );
};

export default AddReferralPage;